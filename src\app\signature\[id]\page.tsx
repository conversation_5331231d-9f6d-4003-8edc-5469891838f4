"use client";

import { useParams } from "next/navigation";
import { useState, useEffect, useRef } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { CheckCircle, AlertCircle, Clock, FileText, Eye, Trash2, Download } from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";
import { toast } from "sonner";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";

interface SignaturePageData {
  form101: {
    id: string;
    taxYear: number;
    maritalStatus: string;
    spouseWorks: boolean;
    childrenCount: number;
    isMainEmployer: boolean;
    hasAdditionalIncome: boolean;
  };
  employee: {
    firstName: string;
    lastName: string;
    nationalId: string;
    email?: string;
    phone?: string;
  };
  employer: {
    name: string;
    businessNumber: string;
    address?: string;
    phone?: string;
  };
  expiresAt: Date;
  status: 'pending' | 'signed' | 'expired' | 'cancelled';
  pdfPreviewUrl?: string;
}

export default function SignaturePage() {
  const params = useParams();
  const signatureId = params?.id as string;

  const [data, setData] = useState<SignaturePageData | null>(null);
  const [loading, setLoading] = useState(true);
  const [signing, setSigning] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showPdfPreview, setShowPdfPreview] = useState(false);
  const [signatureData, setSignatureData] = useState<string | null>(null);
  const [showSignaturePad, setShowSignaturePad] = useState(false);

  const canvasRef = useRef<HTMLCanvasElement>(null);

  useEffect(() => {
    if (signatureId) {
      fetchSignatureData();
    }
  }, [signatureId]);

  const fetchSignatureData = async () => {
    try {
      // TODO: Implement API call to get signature data
      // For now, mock data
      setTimeout(() => {
        setData({
          form101: {
            id: "mock-id",
            taxYear: 2024,
            maritalStatus: "SINGLE",
            spouseWorks: false,
            childrenCount: 0,
            isMainEmployer: true,
            hasAdditionalIncome: false,
          },
          employee: {
            firstName: "יוסי",
            lastName: "כהן",
            nationalId: "*********",
            email: "<EMAIL>",
            phone: "050-1234567",
          },
          employer: {
            name: "אברהם טורגמן משאבי אנוש לעובדים זרים בבנייה בע\"מ",
            businessNumber: "*********",
            address: "הברזל 30, באר שבע",
            phone: "050-2466626",
          },
          expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
          status: 'pending',
          pdfPreviewUrl: "/api/form101/preview/mock-id"
        });
        setLoading(false);
      }, 1000);
    } catch (error) {
      setError("שגיאה בטעינת נתוני החתימה");
      setLoading(false);
    }
  };

  // Signature pad functions
  const startDrawing = (e: React.MouseEvent<HTMLCanvasElement> | React.TouchEvent<HTMLCanvasElement>) => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const rect = canvas.getBoundingClientRect();
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    ctx.beginPath();
    const clientX = 'touches' in e ? e.touches[0]?.clientX ?? 0 : e.clientX;
    const clientY = 'touches' in e ? e.touches[0]?.clientY ?? 0 : e.clientY;
    ctx.moveTo(clientX - rect.left, clientY - rect.top);

    const draw = (moveEvent: MouseEvent | TouchEvent) => {
      const moveClientX = 'touches' in moveEvent ? moveEvent.touches[0]?.clientX ?? 0 : moveEvent.clientX;
      const moveClientY = 'touches' in moveEvent ? moveEvent.touches[0]?.clientY ?? 0 : moveEvent.clientY;
      ctx.lineTo(moveClientX - rect.left, moveClientY - rect.top);
      ctx.stroke();
    };

    const stopDrawing = () => {
      document.removeEventListener('mousemove', draw);
      document.removeEventListener('mouseup', stopDrawing);
      document.removeEventListener('touchmove', draw);
      document.removeEventListener('touchend', stopDrawing);

      // Save signature data
      const dataURL = canvas.toDataURL();
      setSignatureData(dataURL);
    };

    document.addEventListener('mousemove', draw);
    document.addEventListener('mouseup', stopDrawing);
    document.addEventListener('touchmove', draw);
    document.addEventListener('touchend', stopDrawing);
  };

  const clearSignature = () => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    ctx.clearRect(0, 0, canvas.width, canvas.height);
    setSignatureData(null);
  };

  const handleSign = async () => {
    if (!signatureData) {
      toast.error("אנא צייר חתימה לפני השלמת התהליך");
      return;
    }

    setSigning(true);
    try {
      // TODO: Implement actual signature submission to API
      await new Promise(resolve => setTimeout(resolve, 2000));

      setData(prev => prev ? { ...prev, status: 'signed' } : null);
      toast.success("הטופס נחתם בהצלחה!");
      setShowSignaturePad(false);
      setSigning(false);
    } catch (error) {
      console.error("Error signing form:", error);
      toast.error("שגיאה בחתימה על הטופס");
      setSigning(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
        <Card className="w-full max-w-2xl">
          <CardContent className="p-6">
            <Skeleton className="h-8 w-48 mb-4" />
            <Skeleton className="h-4 w-32 mb-6" />
            <Skeleton className="h-32 w-full mb-4" />
            <Skeleton className="h-10 w-32" />
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error || !data) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
        <Card className="w-full max-w-2xl">
          <CardContent className="p-6 text-center">
            <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h1 className="text-xl font-semibold mb-2">שגיאה</h1>
            <p className="text-gray-600 mb-4">{error || "לא ניתן לטעון את נתוני החתימה"}</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  const isExpired = new Date() > data.expiresAt;
  const isSigned = data.status === 'signed';

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <Card className="w-full max-w-2xl">
        <CardHeader className="text-center">
          <div className="flex items-center justify-center gap-2 mb-2">
            <FileText className="h-6 w-6" />
            <CardTitle>חתימה על טופס 101</CardTitle>
          </div>
          <p className="text-sm text-gray-600">
            שנת מס {data.form101.taxYear}
          </p>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* Employee Info */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="font-medium mb-2">פרטי העובד</h3>
            <div className="space-y-1 text-sm">
              <p><span className="font-medium">שם:</span> {data.employee.firstName} {data.employee.lastName}</p>
              <p><span className="font-medium">תעודת זהות:</span> {data.employee.nationalId}</p>
            </div>
          </div>

          {/* Employer Info */}
          <div className="bg-blue-50 p-4 rounded-lg">
            <h3 className="font-medium mb-2">פרטי המעסיק</h3>
            <div className="space-y-1 text-sm">
              <p><span className="font-medium">שם החברה:</span> {data.employer.name}</p>
              <p><span className="font-medium">מספר עסק:</span> {data.employer.businessNumber}</p>
              {data.employer.address && <p><span className="font-medium">כתובת:</span> {data.employer.address}</p>}
              {data.employer.phone && <p><span className="font-medium">טלפון:</span> {data.employer.phone}</p>}
            </div>
          </div>

          {/* Form Info */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="font-medium mb-2">פרטי הטופס</h3>
            <div className="space-y-1 text-sm">
              <p><span className="font-medium">מצב משפחתי:</span> {data.form101.maritalStatus === 'SINGLE' ? 'רווק/ה' : data.form101.maritalStatus}</p>
              <p><span className="font-medium">מספר ילדים:</span> {data.form101.childrenCount}</p>
              <p><span className="font-medium">מעסיק עיקרי:</span> {data.form101.isMainEmployer ? 'כן' : 'לא'}</p>
              <p><span className="font-medium">הכנסה נוספת:</span> {data.form101.hasAdditionalIncome ? 'כן' : 'לא'}</p>
            </div>
          </div>

          {/* PDF Preview */}
          <div className="bg-green-50 p-4 rounded-lg">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-medium mb-1">תצוגה מקדימה של הטופס</h3>
                <p className="text-sm text-gray-600">צפה בטופס 101 לפני החתימה</p>
              </div>
              <Button
                variant="outline"
                onClick={() => setShowPdfPreview(true)}
                className="flex items-center gap-2"
              >
                <Eye className="h-4 w-4" />
                צפה בטופס
              </Button>
            </div>
          </div>

          {/* Status */}
          <div className="text-center">
            {isSigned ? (
              <div className="flex items-center justify-center gap-2 text-green-600 mb-4">
                <CheckCircle className="h-5 w-5" />
                <span className="font-medium">הטופס נחתם בהצלחה</span>
              </div>
            ) : isExpired ? (
              <div className="flex items-center justify-center gap-2 text-red-600 mb-4">
                <AlertCircle className="h-5 w-5" />
                <span className="font-medium">פג תוקף הקישור לחתימה</span>
              </div>
            ) : (
              <div className="flex items-center justify-center gap-2 text-blue-600 mb-4">
                <Clock className="h-5 w-5" />
                <span className="font-medium">ממתין לחתימה</span>
              </div>
            )}

            <p className="text-sm text-gray-600 mb-6">
              תוקף הקישור: {data.expiresAt.toLocaleDateString('he-IL')}
            </p>

            {!isSigned && !isExpired && (
              <Button
                onClick={handleSign}
                disabled={signing}
                className="w-full max-w-xs"
              >
                {signing ? "חותם..." : "חתום על הטופס"}
              </Button>
            )}
          </div>

          {/* Legal Notice */}
          <div className="text-xs text-gray-500 text-center border-t pt-4">
            <p>בחתימה על טופס זה אני מאשר/ת כי הפרטים נכונים ומדויקים.</p>
            <p>החתימה מהווה הסכמה לעיבוד הנתונים לצורכי חישוב מס הכנסה.</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
