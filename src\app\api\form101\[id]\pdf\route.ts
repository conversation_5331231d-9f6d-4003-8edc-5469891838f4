import { NextRequest, NextResponse } from "next/server";
import { db } from "@/server/db";
import { generateForm101PDF } from "@/lib/form101-pdf";

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const form101Id = params.id;

    // Get Form 101 data with employee and tenant information
    const form101 = await db.form101.findUnique({
      where: { id: form101Id },
      include: {
        employee: {
          include: {
            tenant: true
          }
        }
      }
    });

    if (!form101) {
      return NextResponse.json(
        { error: "Form 101 not found" },
        { status: 404 }
      );
    }

    // Prepare employee data
    const employeeData = {
      firstName: form101.employee.firstName,
      lastName: form101.employee.lastName,
      nationalId: form101.employee.nationalId,
      email: form101.employee.email,
      phone: form101.employee.phone,
    };

    // Prepare employer data
    const employerData = {
      name: form101.employee.tenant.name,
      businessNumber: form101.employee.tenant.businessNumber || "",
      address: form101.employee.tenant.address,
      phone: form101.employee.tenant.phone,
    };

    // Generate PDF
    const pdfBuffer = await generateForm101PDF(form101, employeeData, employerData);

    // Return PDF with appropriate headers
    return new NextResponse(pdfBuffer, {
      status: 200,
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': `inline; filename="form101_${form101.employee.firstName}_${form101.employee.lastName}_${form101.taxYear}.pdf"`,
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0',
      },
    });

  } catch (error) {
    console.error("Error generating Form 101 PDF:", error);
    return NextResponse.json(
      { error: "Failed to generate PDF" },
      { status: 500 }
    );
  }
}
