import { NextRequest, NextResponse } from "next/server";
import { db } from "@/server/db";

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const signatureId = params.id;

    // Get signature request from database
    const signatureRequest = await db.signatureRequest.findUnique({
      where: { id: signatureId },
      include: {
        form101: {
          include: {
            employee: {
              include: {
                tenant: true
              }
            }
          }
        }
      }
    });

    if (!signatureRequest) {
      return NextResponse.json(
        { success: false, error: "Signature request not found" },
        { status: 404 }
      );
    }

    // Check if signature request is expired
    const now = new Date();
    const isExpired = now > signatureRequest.expiresAt;

    // Get tenant (employer) information
    const tenant = signatureRequest.form101.employee.tenant;

    const responseData = {
      form101: {
        id: signatureRequest.form101.id,
        taxYear: signatureRequest.form101.taxYear,
        maritalStatus: signatureRequest.form101.maritalStatus,
        spouseWorks: signatureRequest.form101.spouseWorks,
        childrenCount: signatureRequest.form101.childrenCount,
        isMainEmployer: signatureRequest.form101.isMainEmployer,
        hasAdditionalIncome: signatureRequest.form101.hasAdditionalIncome,
      },
      employee: {
        firstName: signatureRequest.form101.employee.firstName,
        lastName: signatureRequest.form101.employee.lastName,
        nationalId: signatureRequest.form101.employee.nationalId,
        email: signatureRequest.form101.employee.email,
        phone: signatureRequest.form101.employee.phone,
      },
      employer: {
        name: tenant.name,
        businessNumber: tenant.businessNumber || "",
        address: tenant.address,
        phone: tenant.phone,
      },
      expiresAt: signatureRequest.expiresAt,
      status: isExpired ? 'expired' : signatureRequest.status,
      pdfPreviewUrl: `/api/form101/${signatureRequest.form101.id}/pdf`
    };

    return NextResponse.json({
      success: true,
      data: responseData
    });

  } catch (error) {
    console.error("Error fetching signature data:", error);
    return NextResponse.json(
      { success: false, error: "Internal server error" },
      { status: 500 }
    );
  }
}
