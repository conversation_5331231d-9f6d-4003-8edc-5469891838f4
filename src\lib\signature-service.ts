import { type Form101 } from "@prisma/client";
import { sendTemplatedSMS, validateIsraeliPhoneNumber, formatPhoneForSMS, SMS_TEMPLATES } from "@/server/sms";
import { db } from "@/server/db";

export interface SignatureRequest {
  id: string;
  status: 'pending' | 'signed' | 'expired' | 'cancelled';
  documentUrl?: string;
  signedAt?: Date;
  expiresAt: Date;
}

export interface SignatureServiceConfig {
  apiKey: string;
  apiUrl: string;
}

export class SignatureService {
  private config: SignatureServiceConfig;

  constructor(config: SignatureServiceConfig) {
    this.config = config;
  }

  async sendForSignature(params: {
    form101: Form101;
    employeeEmail: string;
    employeePhone?: string;
    documentData: Buffer;
  }): Promise<SignatureRequest> {
    const { form101, employeeEmail, employeePhone, documentData } = params;

    // Set expiry date (7 days from now)
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 7);

    // Create signature request in database
    const signatureRequest = await db.signatureRequest.create({
      data: {
        tenantId: form101.tenantId,
        form101Id: form101.id,
        status: 'PENDING',
        expiresAt,
      }
    });

    // Update Form 101 status and signature request info
    await db.form101.update({
      where: { id: form101.id },
      data: {
        status: 'PENDING_SIGNATURE',
        signatureRequestId: signatureRequest.id,
        signatureRequestSentAt: new Date(),
        signatureRequestExpiresAt: expiresAt,
      }
    });

    // Create signature URL
    const signatureUrl = `${process.env.NEXTAUTH_URL || 'https://salary-t3.vercel.app'}/signature/${signatureRequest.id}`;

    // Get employee details for SMS
    const employee = await db.employee.findUnique({
      where: { id: form101.employeeId },
      select: {
        firstName: true,
        lastName: true,
        contact: true,
        tenantId: true
      }
    });

    if (!employee) {
      throw new Error('Employee not found');
    }

    // Extract phone from contact
    const contact = employee.contact as any || {};
    const phone = employeePhone || contact.phone;

    if (phone && validateIsraeliPhoneNumber(phone)) {
      const formattedPhone = formatPhoneForSMS(phone);

      // Send SMS using template
      try {
        await sendTemplatedSMS(
          formattedPhone,
          SMS_TEMPLATES.FORM101_SIGNATURE,
          {
            firstName: employee.firstName,
            taxYear: (form101.taxYear || new Date().getFullYear()).toString(),
            signatureUrl: signatureUrl,
            expiryDate: expiresAt.toLocaleDateString('he-IL')
          },
          form101.employeeId,
          employee.tenantId
        );
        console.log(`SMS sent successfully to ${formattedPhone} for Form 101 signature`);
      } catch (error) {
        console.error('Failed to send SMS for Form 101 signature:', error);
        // Don't throw error - continue with signature request creation
      }
    } else {
      console.warn(`No valid phone number found for employee ${form101.employeeId}`);
    }

    // Also send email if available
    if (employeeEmail) {
      // TODO: Implement email sending
      console.log(`Email notification should be sent to ${employeeEmail}`);
    }

    return {
      id: signatureRequest.id,
      status: 'pending',
      expiresAt,
      documentUrl: signatureUrl
    };
  }

  async checkStatus(signatureRequestId: string): Promise<SignatureRequest> {
    // TODO: Implement actual signature service integration
    // For now, return mock data
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 7); // 7 days expiry

    return {
      id: signatureRequestId,
      status: 'pending',
      expiresAt
    };
  }

  async cancelRequest(signatureRequestId: string): Promise<void> {
    // TODO: Implement actual signature service integration
  }
}

// Create singleton instance
let signatureService: SignatureService | null = null;

export function getSignatureService(): SignatureService {
  if (!signatureService) {
    // TODO: Get config from environment variables
    signatureService = new SignatureService({
      apiKey: process.env.SIGNATURE_SERVICE_API_KEY || 'mock_key',
      apiUrl: process.env.SIGNATURE_SERVICE_API_URL || 'https://api.signature-service.example.com'
    });
  }
  return signatureService;
}