import { NextRequest, NextResponse } from "next/server";
import { db } from "@/server/db";

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const signatureId = params.id;
    const body = await request.json();
    const { signatureData, timestamp } = body;

    if (!signatureData) {
      return NextResponse.json(
        { success: false, error: "Signature data is required" },
        { status: 400 }
      );
    }

    // Get signature request from database
    const signatureRequest = await db.signatureRequest.findUnique({
      where: { id: signatureId },
      include: {
        form101: {
          include: {
            employee: true
          }
        }
      }
    });

    if (!signatureRequest) {
      return NextResponse.json(
        { success: false, error: "Signature request not found" },
        { status: 404 }
      );
    }

    // Check if signature request is expired
    const now = new Date();
    const isExpired = now > signatureRequest.expiresAt;

    if (isExpired) {
      return NextResponse.json(
        { success: false, error: "Signature request has expired" },
        { status: 400 }
      );
    }

    // Check if already signed
    if (signatureRequest.status === 'signed') {
      return NextResponse.json(
        { success: false, error: "Form has already been signed" },
        { status: 400 }
      );
    }

    // Update signature request status and save signature data
    const updatedSignatureRequest = await db.signatureRequest.update({
      where: { id: signatureId },
      data: {
        status: 'signed',
        signedAt: new Date(timestamp || Date.now()),
        signatureData: signatureData,
      }
    });

    // Update Form 101 status
    await db.form101.update({
      where: { id: signatureRequest.form101Id },
      data: {
        status: 'SIGNED',
        signedAt: new Date(timestamp || Date.now()),
      }
    });

    // Log the signature event
    console.log(`Form 101 signed: ${signatureRequest.form101Id} by employee: ${signatureRequest.form101.employee.firstName} ${signatureRequest.form101.employee.lastName}`);

    return NextResponse.json({
      success: true,
      data: {
        id: updatedSignatureRequest.id,
        status: updatedSignatureRequest.status,
        signedAt: updatedSignatureRequest.signedAt,
      }
    });

  } catch (error) {
    console.error("Error signing form:", error);
    return NextResponse.json(
      { success: false, error: "Internal server error" },
      { status: 500 }
    );
  }
}
