import { type Form101 } from "@prisma/client";
import { PDFDocument, rgb, StandardFonts } from 'pdf-lib';
import fs from 'fs/promises';
import path from 'path';

// Coordinate configuration for Form 101 fields
// Adjust these values based on your actual PDF template
//
// HOW TO FIND CORRECT COORDINATES:
// 1. Open your PDF in a PDF viewer and note where each field should be placed
// 2. PDF coordinates start from bottom-left (0,0)
// 3. X increases going right, Y increases going up
// 4. The function uses (height - y) to convert from top-down positioning
// 5. Test with different values and check the preview to fine-tune
//
// CURRENT VALUES ARE ESTIMATES - ADJUST BASED ON YOUR ACTUAL FORM 101 PDF
const FORM_COORDINATES = {
  employeeName: { x: 150, y: 150 },      // Employee full name
  nationalId: { x: 150, y: 180 },        // National ID number
  taxYear: { x: 150, y: 210 },           // Tax year
  childrenCount: { x: 150, y: 240 },     // Number of children
  maritalStatus: { x: 150, y: 270 },     // Marital status
  employerName: { x: 150, y: 300 },      // Employer name
  businessNumber: { x: 150, y: 330 },    // Business number
  date: { x: 150, y: 360 },              // Current date
  booleanFields: { x: 150, y: 400 },     // Checkboxes (main employer, spouse works, etc.)
  additionalFields: { x: 150, y: 500 }   // Additional fields (credit points, etc.)
};

// Helper function to create a coordinate mapping guide PDF
// This can help you visualize where each field will be placed
export async function createCoordinateGuide(): Promise<Buffer> {
  try {
    const templatePath = path.join(process.cwd(), 'public', '101.pdf');
    const templateBytes = await fs.readFile(templatePath);
    const pdfDoc = await PDFDocument.load(templateBytes);
    const pages = pdfDoc.getPages();
    const firstPage = pages[0]!;
    const { height } = firstPage.getSize();

    const font = await pdfDoc.embedFont(StandardFonts.Helvetica);
    const fontSize = 10;
    const textColor = rgb(1, 0, 0); // Red color for visibility

    // Add coordinate labels for each field
    Object.entries(FORM_COORDINATES).forEach(([fieldName, coords]) => {
      const label = `${fieldName}: (${coords.x}, ${height - coords.y})`;
      firstPage.drawText(label, {
        x: coords.x,
        y: height - coords.y,
        size: fontSize,
        font: font,
        color: textColor,
      });

      // Add a small cross to mark the exact position
      firstPage.drawText('+', {
        x: coords.x - 5,
        y: height - coords.y - 5,
        size: fontSize + 2,
        font: font,
        color: textColor,
      });
    });

    const pdfBytes = await pdfDoc.save();
    return Buffer.from(pdfBytes);
  } catch (error) {
    console.error('Error creating coordinate guide:', error);
    throw new Error('Failed to create coordinate guide');
  }
}

export async function generateForm101PDF(
  form101: Form101,
  employeeData: {
    firstName: string;
    lastName: string;
    nationalId: string;
    email?: string | null;
    phone?: string | null;
  },
  employerData?: {
    name: string;
    businessNumber: string;
    address?: string;
    phone?: string;
  }
): Promise<Buffer> {
  try {
    // Load the template PDF
    const templatePath = path.join(process.cwd(), 'public', '101.pdf');
    const templateBytes = await fs.readFile(templatePath);

    // Load the PDF document
    const pdfDoc = await PDFDocument.load(templateBytes);
    const form = pdfDoc.getForm();

    // Debug: Log available fields in the PDF
    const fields = form.getFields();
    console.log('Available PDF fields:', fields.map(field => ({
      name: field.getName(),
      type: field.constructor.name
    })));

    // Since the PDF has no form fields, we'll add text directly to the PDF
    const pages = pdfDoc.getPages();
    const firstPage = pages[0];

    if (!firstPage) {
      throw new Error('PDF has no pages');
    }

    // Get page dimensions
    const { width, height } = firstPage.getSize();
    console.log(`PDF page size: ${width} x ${height}`);

    // Load a font for Hebrew text support
    const font = await pdfDoc.embedFont(StandardFonts.Helvetica);
    const fontSize = 12;
    const textColor = rgb(0, 0, 0); // Black

    try {
      // Employee details
      const employeeName = `${employeeData.firstName} ${employeeData.lastName}`;
      const currentDate = new Date();
      const taxYear = form101.taxYear || currentDate.getFullYear();

      // Since the PDF has no form fields, we'll add text directly at specific coordinates
      // These coordinates are estimates and may need adjustment based on the actual PDF layout
      // To find the correct coordinates, you can:
      // 1. Open the PDF and measure where fields should be placed
      // 2. Use PDF editing software to identify exact coordinates
      // 3. Adjust the x,y values below based on your specific Form 101 template

      console.log('📝 Adding text directly to PDF...');
      console.log(`📐 PDF dimensions: ${width} x ${height}`);

      // Add employee name (assuming it goes in the top section)
      firstPage.drawText(employeeName, {
        x: FORM_COORDINATES.employeeName.x,
        y: height - FORM_COORDINATES.employeeName.y,
        size: fontSize,
        font: font,
        color: textColor,
      });
      console.log(`✅ Added employee name: ${employeeName}`);

      // Add national ID
      firstPage.drawText(employeeData.nationalId, {
        x: FORM_COORDINATES.nationalId.x,
        y: height - FORM_COORDINATES.nationalId.y,
        size: fontSize,
        font: font,
        color: textColor,
      });
      console.log(`✅ Added national ID: ${employeeData.nationalId}`);

      // Add tax year
      firstPage.drawText(taxYear.toString(), {
        x: FORM_COORDINATES.taxYear.x,
        y: height - FORM_COORDINATES.taxYear.y,
        size: fontSize,
        font: font,
        color: textColor,
      });
      console.log(`✅ Added tax year: ${taxYear}`);

      // Add children count
      firstPage.drawText(form101.childrenCount.toString(), {
        x: FORM_COORDINATES.childrenCount.x,
        y: height - FORM_COORDINATES.childrenCount.y,
        size: fontSize,
        font: font,
        color: textColor,
      });
      console.log(`✅ Added children count: ${form101.childrenCount}`);

      // Add marital status in Hebrew
      const maritalStatusHebrew = form101.maritalStatus === 'SINGLE' ? 'רווק/ה' :
                                 form101.maritalStatus === 'MARRIED' ? 'נשוי/ה' :
                                 form101.maritalStatus === 'DIVORCED' ? 'גרוש/ה' :
                                 form101.maritalStatus === 'WIDOWED' ? 'אלמן/ה' : '';

      if (maritalStatusHebrew) {
        firstPage.drawText(maritalStatusHebrew, {
          x: FORM_COORDINATES.maritalStatus.x,
          y: height - FORM_COORDINATES.maritalStatus.y,
          size: fontSize,
          font: font,
          color: textColor,
        });
        console.log(`✅ Added marital status: ${maritalStatusHebrew}`);
      }

      // Add employer information if provided
      if (employerData) {
        firstPage.drawText(employerData.name, {
          x: FORM_COORDINATES.employerName.x,
          y: height - FORM_COORDINATES.employerName.y,
          size: fontSize,
          font: font,
          color: textColor,
        });
        console.log(`✅ Added employer name: ${employerData.name}`);

        firstPage.drawText(employerData.businessNumber, {
          x: FORM_COORDINATES.businessNumber.x,
          y: height - FORM_COORDINATES.businessNumber.y,
          size: fontSize,
          font: font,
          color: textColor,
        });
        console.log(`✅ Added business number: ${employerData.businessNumber}`);
      }

      // Add current date
      const dateString = currentDate.toLocaleDateString('he-IL');
      firstPage.drawText(dateString, {
        x: FORM_COORDINATES.date.x,
        y: height - FORM_COORDINATES.date.y,
        size: fontSize,
        font: font,
        color: textColor,
      });
      console.log(`✅ Added date: ${dateString}`);

      // Add boolean fields as checkmarks or text
      const booleanY = height - FORM_COORDINATES.booleanFields.y;
      let yOffset = 0;

      if (form101.isMainEmployer) {
        firstPage.drawText('✓ מעסיק עיקרי', {
          x: FORM_COORDINATES.booleanFields.x,
          y: booleanY - yOffset,
          size: fontSize,
          font: font,
          color: textColor,
        });
        yOffset += 30;
        console.log('✅ Added main employer checkbox');
      }

      if (form101.spouseWorks) {
        firstPage.drawText('✓ בן/בת זוג עובד/ת', {
          x: FORM_COORDINATES.booleanFields.x,
          y: booleanY - yOffset,
          size: fontSize,
          font: font,
          color: textColor,
        });
        yOffset += 30;
        console.log('✅ Added spouse works checkbox');
      }

      if (form101.hasAdditionalIncome) {
        firstPage.drawText('✓ הכנסה נוספת', {
          x: FORM_COORDINATES.booleanFields.x,
          y: booleanY - yOffset,
          size: fontSize,
          font: font,
          color: textColor,
        });
        yOffset += 30;
        console.log('✅ Added additional income checkbox');
      }

      console.log('📊 Successfully added all available data to PDF');

      // Add additional fields if they exist
      let additionalY = height - FORM_COORDINATES.additionalFields.y;

      if (form101.additionalCreditPoints) {
        firstPage.drawText(`נקודות זיכוי נוספות: ${form101.additionalCreditPoints}`, {
          x: FORM_COORDINATES.additionalFields.x,
          y: additionalY,
          size: fontSize,
          font: font,
          color: textColor,
        });
        additionalY -= 30;
        console.log(`✅ Added additional credit points: ${form101.additionalCreditPoints}`);
      }

      if (form101.taxCoordinationNumber) {
        firstPage.drawText(`מספר תיאום מס: ${form101.taxCoordinationNumber}`, {
          x: FORM_COORDINATES.additionalFields.x,
          y: additionalY,
          size: fontSize,
          font: font,
          color: textColor,
        });
        additionalY -= 30;
        console.log(`✅ Added tax coordination number: ${form101.taxCoordinationNumber}`);
      }

      if (form101.childrenUnder18 && form101.childrenUnder18 > 0) {
        firstPage.drawText(`ילדים מתחת לגיל 18: ${form101.childrenUnder18}`, {
          x: FORM_COORDINATES.additionalFields.x,
          y: additionalY,
          size: fontSize,
          font: font,
          color: textColor,
        });
        additionalY -= 30;
        console.log(`✅ Added children under 18: ${form101.childrenUnder18}`);
      }

      if (form101.childrenUnder5 && form101.childrenUnder5 > 0) {
        firstPage.drawText(`ילדים מתחת לגיל 5: ${form101.childrenUnder5}`, {
          x: FORM_COORDINATES.additionalFields.x,
          y: additionalY,
          size: fontSize,
          font: font,
          color: textColor,
        });
        additionalY -= 30;
        console.log(`✅ Added children under 5: ${form101.childrenUnder5}`);
      }

    } catch (error) {
      console.warn('Some form fields could not be filled:', error);
      // Continue with PDF generation even if some fields fail
    }

    // Save the filled PDF
    const pdfBytes = await pdfDoc.save();

    console.log(`🎉 Form 101 PDF generated successfully for ${employeeData.firstName} ${employeeData.lastName}`);
    if (employerData) {
      console.log(`📋 Employer: ${employerData.name} (${employerData.businessNumber})`);
    }

    return Buffer.from(pdfBytes);
  } catch (error) {
    console.error('Error generating Form 101 PDF:', error);
    throw new Error('Failed to generate Form 101 PDF');
  }
}